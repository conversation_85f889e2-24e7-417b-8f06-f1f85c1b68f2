{"name": "orbit-backend", "version": "1.0.1", "description": "Orbit Backend API for cPanel deployment", "main": "app.js", "scripts": {"start": "node app.js"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.321.1", "@aws-sdk/s3-request-presigner": "^3.327.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^1.0.1", "@nestjs/common": "^9.4.0", "@nestjs/config": "^2.3.1", "@nestjs/core": "^9.4.0", "@nestjs/event-emitter": "^1.4.1", "@nestjs/jwt": "^10.0.3", "@nestjs/mapped-types": "^1.2.2", "@nestjs/mongoose": "^9.2.2", "@nestjs/platform-express": "^9.0.0", "@nestjs/platform-socket.io": "^9.4.0", "@nestjs/schedule": "^2.2.1", "@nestjs/throttler": "^4.0.0", "@nestjs/websockets": "^9.4.0", "@parse/node-apn": "^6.3.0", "@socket.io/admin-ui": "^0.5.1", "@types/utf8": "^3.0.1", "agora-token": "^2.0.3", "app-root-path": "^3.1.0", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "class-sanitizer": "^1.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "crypto-js": "^4.1.1", "date-and-time": "^3.0.0", "date-fns": "^2.29.3", "dotenv": "^16.0.0", "file-type": "^16.5.3", "firebase-admin": "^11.11.0", "geoip-lite": "^1.4.6", "handlebars": "^4.7.8", "helmet": "^6.1.5", "image-size": "^1.0.2", "link-preview-js": "^3.0.5", "mongoose": "^7.0.4", "mongoose-aggregate-paginate-v2": "^1.0.6", "mongoose-paginate-v2": "^1.7.1", "morgan": "^1.10.0", "nodemailer": "^6.7.5", "onesignal-api-client-core": "^1.2.2", "onesignal-api-client-nest": "^1.0.10", "onesignal-node": "^3.4.0", "os": "^0.1.2", "parse-env-to-json": "^1.0.4", "redis": "^4.5.1", "reflect-metadata": "^0.1.13", "remove-accents": "^0.4.4", "request-ip": "^3.3.0", "rxjs": "^7.8.0", "semver": "^7.3.8", "sharp": "^0.32.0", "socket.io": "^4.6.1", "typescript": "4.8.4", "utf8": "^3.0.0", "uuid": "^9.0.0", "xss-clean": "^0.1.1"}}